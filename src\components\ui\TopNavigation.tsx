import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './button';
import { Avatar, AvatarFallback } from './avatar';
import { Badge } from './badge';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import {
  BellIcon,
  ChevronDownIcon,
  PersonIcon,
  GearIcon,
  ExitIcon
} from '@radix-ui/react-icons';

export const TopNavigation: React.FC = () => {
  const { themeConfig } = useGradientTheme();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const notifications = [
    { id: 1, title: 'Grievance Updated', message: 'Your grievance #GR-001 has been resolved', time: '2 min ago' },
    { id: 2, title: 'New Message', message: '<PERSON><PERSON> replied to your query', time: '1 hour ago' },
    { id: 3, title: 'System Update', message: 'Platform maintenance scheduled', time: '3 hours ago' }
  ];

  return (
    <motion.header
      className="fixed top-0 right-0 left-[270px] h-16 z-40 px-6"
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <div
        className="h-full rounded-2xl mt-4 px-6 flex items-center justify-end gap-4"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          background: 'var(--glass-bg)',
          border: '1px solid var(--glass-border)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* Notifications */}
        <div className="relative">
          <Button
            variant="ghost"
            size="sm"
            className="relative p-2 rounded-xl"
            style={{ color: 'var(--theme-text)' }}
            onClick={() => setIsNotificationOpen(!isNotificationOpen)}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <BellIcon className="w-5 h-5" />
            <Badge 
              className="absolute -top-1 -right-1 w-5 h-5 rounded-full p-0 flex items-center justify-center text-xs font-medium"
              style={{ 
                backgroundColor: themeConfig.accentColor,
                color: 'white'
              }}
            >
              3
            </Badge>
          </Button>

          {/* Notification Dropdown */}
          <AnimatePresence>
            {isNotificationOpen && (
              <motion.div
                className="absolute top-full right-0 mt-2 w-80 rounded-xl p-4"
                style={{
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  background: 'var(--glass-bg)',
                  border: '1px solid var(--glass-border)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
                }}
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <h3 
                  className="font-semibold mb-3"
                  style={{ color: 'var(--theme-text)' }}
                >
                  Notifications
                </h3>
                <div className="space-y-3">
                  {notifications.map((notification, index) => (
                    <motion.div
                      key={notification.id}
                      className="p-3 rounded-lg cursor-pointer transition-all duration-200"
                      style={{
                        backgroundColor: 'transparent',
                        border: '1px solid var(--glass-border)'
                      }}
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = 'transparent';
                      }}
                    >
                      <div 
                        className="font-medium text-sm"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {notification.title}
                      </div>
                      <div 
                        className="text-xs opacity-70 mt-1"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {notification.message}
                      </div>
                      <div 
                        className="text-xs opacity-50 mt-1"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {notification.time}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Profile Menu */}
        <div className="relative">
          <Button
            variant="ghost"
            className="flex items-center gap-2 px-3 py-2 rounded-xl"
            style={{ color: 'var(--theme-text)' }}
            onClick={() => setIsProfileOpen(!isProfileOpen)}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
            }}
          >
            <Avatar className="h-8 w-8">
              <AvatarFallback 
                className="text-white font-medium text-sm"
                style={{ backgroundColor: themeConfig.accentColor }}
              >
                U
              </AvatarFallback>
            </Avatar>
            <ChevronDownIcon 
              className={`w-4 h-4 transition-transform duration-200 ${isProfileOpen ? 'rotate-180' : ''}`} 
            />
          </Button>

          {/* Profile Dropdown */}
          <AnimatePresence>
            {isProfileOpen && (
              <motion.div
                className="absolute top-full right-0 mt-2 w-48 rounded-xl p-2"
                style={{
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  background: 'var(--glass-bg)',
                  border: '1px solid var(--glass-border)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
                }}
                initial={{ opacity: 0, scale: 0.95, y: -10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: -10 }}
                transition={{ duration: 0.2 }}
              >
                <Button
                  variant="ghost"
                  className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start"
                  style={{ color: 'var(--theme-text)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <PersonIcon className="w-4 h-4" />
                  <span className="text-sm">Profile</span>
                </Button>
                <Button
                  variant="ghost"
                  className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start"
                  style={{ color: 'var(--theme-text)' }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <GearIcon className="w-4 h-4" />
                  <span className="text-sm">Settings</span>
                </Button>
                <div className="h-px bg-white/10 my-2" />
                <Button
                  variant="ghost"
                  className="w-full flex items-center gap-2 px-3 py-2 rounded-lg justify-start text-red-400"
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <ExitIcon className="w-4 h-4" />
                  <span className="text-sm">Logout</span>
                </Button>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.header>
  );
};