export interface GradientTheme {
  name: string;
  displayName: string;
  gradient: string;
  textColor: string;
  accentColor: string;
}

export const gradientThemes: Record<string, GradientTheme> = {
  lightBreeze: {
    name: 'lightBreeze',
    displayName: 'Light Breeze',
    gradient: 'linear-gradient(135deg, #ffffff 0%, #f0f9ff 20%, #e0f2fe 40%, #bae6fd 60%, #7dd3fc 80%, #38bdf8 100%)',
    textColor: '#0f172a',
    accentColor: '#0ea5e9'
  },
  darkKnight: {
    name: 'darkKnight',
    displayName: 'Dark Knight',
    gradient: 'linear-gradient(135deg, #000000 0%, #0f0f23 20%, #1e1e3f 40%, #2d2d5a 60%, #3c3c76 80%, #4b4b91 100%)',
    textColor: '#ffffff',
    accentColor: '#6366f1'
  },
  galaxySpiral: {
    name: 'galaxySpiral',
    displayName: 'Galaxy Spiral',
    gradient: 'linear-gradient(135deg, #1e1b4b 0%, #312e81 20%, #4c1d95 40%, #7c3aed 60%, #a855f7 80%, #c084fc 100%)',
    textColor: '#ffffff',
    accentColor: '#a855f7'
  },
  cosmicDust: {
    name: 'cosmicDust',
    displayName: 'Cosmic Dust',
    gradient: 'linear-gradient(135deg, #7c2d12 0%, #9a3412 20%, #c2410c 40%, #ea580c 60%, #f97316 80%, #fb923c 100%)',
    textColor: '#ffffff',
    accentColor: '#f97316'
  },
  arcticChill: {
    name: 'arcticChill',
    displayName: 'Arctic Chill',
    gradient: 'linear-gradient(135deg, #ffffff 0%, #f0f9ff 20%, #e0f2fe 40%, #bae6fd 60%, #0891b2 80%, #164e63 100%)',
    textColor: '#0f172a',
    accentColor: '#0284c7'
  },
  volcanoEmber: {
    name: 'volcanoEmber',
    displayName: 'Volcano Ember',
    gradient: 'linear-gradient(135deg, #7f1d1d 0%, #991b1b 20%, #dc2626 40%, #ef4444 60%, #f87171 80%, #fca5a5 100%)',
    textColor: '#ffffff',
    accentColor: '#ef4444'
  },
  forestWhisper: {
    name: 'forestWhisper',
    displayName: 'Forest Whisper',
    gradient: 'linear-gradient(135deg, #14532d 0%, #166534 20%, #15803d 40%, #16a34a 60%, #22c55e 80%, #4ade80 100%)',
    textColor: '#ffffff',
    accentColor: '#16a34a'
  },
  oceanDeep: {
    name: 'oceanDeep',
    displayName: 'Ocean Deep',
    gradient: 'linear-gradient(135deg, #164e63 0%, #0e7490 20%, #0891b2 40%, #06b6d4 60%, #22d3ee 80%, #67e8f9 100%)',
    textColor: '#ffffff',
    accentColor: '#0ea5e9'
  },
  sunsetGlow: {
    name: 'sunsetGlow',
    displayName: 'Sunset Glow',
    gradient: 'linear-gradient(135deg, #f97316 0%, #fb923c 20%, #fdba74 40%, #fed7aa 60%, #fef3c7 80%, #fffbeb 100%)',
    textColor: '#78350f',
    accentColor: '#f59e0b'
  },
  cyberpunkNeon: {
    name: 'cyberpunkNeon',
    displayName: 'Cyberpunk Neon',
    gradient: 'linear-gradient(135deg, #0f0f0f 0%, #1a1a2e 20%, #16213e 40%, #0f3460 60%, #533483 80%, #e94560 100%)',
    textColor: '#ffffff',
    accentColor: '#06b6d4'
  },
  crystalShard: {
    name: 'crystalShard',
    displayName: 'Crystal Shard',
    gradient: 'linear-gradient(135deg, #ffffff 0%, #f8fafc 20%, #f1f5f9 40%, #e2e8f0 60%, #cbd5e1 80%, #94a3b8 100%)',
    textColor: '#1e293b',
    accentColor: '#3b82f6'
  },
  monochrome: {
    name: 'monochrome',
    displayName: 'Monochrome Slate',
    gradient: 'linear-gradient(135deg, #f8fafc 0%, #f1f5f9 20%, #e2e8f0 40%, #cbd5e1 60%, #94a3b8 80%, #64748b 100%)',
    textColor: '#0f172a',
    accentColor: '#64748b'
  },
  goldenHour: {
    name: 'goldenHour',
    displayName: 'Golden Hour',
    gradient: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 20%, #fcd34d 40%, #f59e0b 60%, #d97706 80%, #92400e 100%)',
    textColor: '#78350f',
    accentColor: '#f59e0b'
  },
  retroWave: {
    name: 'retroWave',
    displayName: 'Retro Wave',
    gradient: 'linear-gradient(135deg, #1a1a2e 0%, #16213e 20%, #0f3460 40%, #533483 60%, #e94560 80%, #f39c12 100%)',
    textColor: '#ffffff',
    accentColor: '#ec4899'
  },
  emeraldCity: {
    name: 'emeraldCity',
    displayName: 'Emerald City',
    gradient: 'linear-gradient(135deg, #064e3b 0%, #047857 20%, #059669 40%, #10b981 60%, #34d399 80%, #6ee7b7 100%)',
    textColor: '#ffffff',
    accentColor: '#10b981'
  },
  midnightExpress: {
    name: 'midnightExpress',
    displayName: 'Midnight Express',
    gradient: 'linear-gradient(135deg, #000000 0%, #0c0c0c 20%, #1a1a1a 40%, #262626 60%, #404040 80%, #525252 100%)',
    textColor: '#ffffff',
    accentColor: '#6b7280'
  }
};

export type GradientThemeName = keyof typeof gradientThemes;

// Arctic Chill inspired gradient effects with smooth transitions
// Each gradient now features 6 color stops for smoother, more sophisticated transitions
// Similar to Arctic Chill's crisp, layered progression from light to deep tones