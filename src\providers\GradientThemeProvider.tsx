import React, { createContext, useContext, useState, useEffect } from 'react';
import { gradientThemes, GradientThemeName } from '../types/gradientThemes';

interface GradientThemeContextType {
  currentTheme: GradientThemeName;
  setTheme: (theme: GradientThemeName) => void;
  themeConfig: typeof gradientThemes[GradientThemeName];
}

const GradientThemeContext = createContext<GradientThemeContextType | undefined>(undefined);

export const useGradientTheme = () => {
  const context = useContext(GradientThemeContext);
  if (!context) {
    throw new Error('useGradientTheme must be used within GradientThemeProvider');
  }
  return context;
};

export const GradientThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [currentTheme, setCurrentTheme] = useState<GradientThemeName>('lightBreeze');

  const setTheme = (theme: GradientThemeName) => {
    setCurrentTheme(theme);
    localStorage.setItem('gradient-theme', theme);
    
    // Apply CSS variables to root
    const root = document.documentElement;
    const themeConfig = gradientThemes[theme];
    
    root.style.setProperty('--gradient-bg', themeConfig.gradient);
    root.style.setProperty('--theme-text', themeConfig.textColor);
    root.style.setProperty('--theme-accent', themeConfig.accentColor);
    root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.05)');
    root.style.setProperty('--glass-border', 'rgba(255, 255, 255, 0.15)');
    root.style.setProperty('--glass-hover', 'rgba(255, 255, 255, 0.1)');
  };

  useEffect(() => {
    const savedTheme = localStorage.getItem('gradient-theme') as GradientThemeName;
    if (savedTheme && gradientThemes[savedTheme]) {
      setTheme(savedTheme);
    } else {
      setTheme('lightBreeze');
    }
  }, []);

  const value = {
    currentTheme,
    setTheme,
    themeConfig: gradientThemes[currentTheme]
  };

  return (
    <GradientThemeContext.Provider value={value}>
      <div 
        className="min-h-screen transition-all duration-500"
        style={{ background: gradientThemes[currentTheme].gradient }}
      >
        {children}
      </div>
    </GradientThemeContext.Provider>
  );
};