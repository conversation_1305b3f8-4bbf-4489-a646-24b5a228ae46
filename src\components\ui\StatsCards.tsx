import React from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from './card';
import { Badge } from './badge';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import {
  FileIcon,
  CheckCircledIcon,
  ClockIcon,
  BarChartIcon
} from '@radix-ui/react-icons';

interface StatCard {
  title: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative' | 'neutral';
  icon: React.ComponentType<{ className?: string }>;
  badge?: string;
}

const statsData: StatCard[] = [
  {
    title: 'Total Grievances',
    value: '24',
    change: '+2 this month',
    changeType: 'positive',
    icon: FileIcon
  },
  {
    title: 'Resolved',
    value: '16',
    change: '66.7% rate',
    changeType: 'positive',
    icon: CheckCircledIcon,
    badge: 'Success'
  },
  {
    title: 'In Progress',
    value: '6',
    change: 'Being processed',
    changeType: 'neutral',
    icon: ClockIcon,
    badge: 'Processing'
  },
  {
    title: 'Avg. Resolution',
    value: '7.2',
    change: 'days average',
    changeType: 'positive',
    icon: BarChartIcon,
    badge: 'Improved'
  }
];

export const StatsCards: React.FC = () => {
  const { themeConfig } = useGradientTheme();

  const getBadgeColor = (type: string) => {
    switch (type) {
      case 'Success':
        return '#10b981';
      case 'Processing':
        return '#f59e0b';
      case 'Improved':
        return '#3b82f6';
      default:
        return themeConfig.accentColor;
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsData.map((stat, index) => (
        <motion.div
          key={stat.title}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.5 + index * 0.1 }}
          whileHover={{ scale: 1.02, y: -2 }}
        >
          <Card
            className="relative overflow-hidden border-0 transition-all duration-300"
            style={{
              backdropFilter: 'blur(20px)',
              WebkitBackdropFilter: 'blur(20px)',
              background: 'var(--glass-bg)',
              border: '1px solid var(--glass-border)',
              boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
            }}
          >
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <p 
                    className="text-xs font-semibold uppercase tracking-wide opacity-70 mb-2"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    {stat.title}
                  </p>
                  <div className="flex items-baseline gap-2 mb-2">
                    <motion.span 
                      className="text-2xl font-bold"
                      style={{ color: 'var(--theme-text)' }}
                      initial={{ opacity: 0, scale: 0.5 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ duration: 0.5, delay: 0.7 + index * 0.1 }}
                    >
                      {stat.value}
                    </motion.span>
                    {stat.badge && (
                      <Badge 
                        className="text-xs px-2 py-1 rounded-full font-medium"
                        style={{ 
                          backgroundColor: `${getBadgeColor(stat.badge)}20`,
                          color: getBadgeColor(stat.badge),
                          border: `1px solid ${getBadgeColor(stat.badge)}40`
                        }}
                      >
                        {stat.badge}
                      </Badge>
                    )}
                  </div>
                  <p 
                    className="text-xs opacity-60"
                    style={{ color: 'var(--theme-text)' }}
                  >
                    {stat.change}
                  </p>
                </div>
                
                <motion.div
                  className="p-3 rounded-xl"
                  style={{
                    backgroundColor: `${themeConfig.accentColor}20`,
                    border: `1px solid ${themeConfig.accentColor}30`
                  }}
                  initial={{ opacity: 0, rotate: -10 }}
                  animate={{ opacity: 1, rotate: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 + index * 0.1 }}
                >
                  <stat.icon 
                    className="w-6 h-6"
                    style={{ color: themeConfig.accentColor }}
                  />
                </motion.div>
              </div>

              {/* Progress Bar for Resolution Rate */}
              {stat.title === 'Resolved' && (
                <motion.div
                  className="mt-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 + index * 0.1 }}
                >
                  <div className="flex justify-between text-xs mb-1">
                    <span style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                      Progress
                    </span>
                    <span style={{ color: 'var(--theme-text)', opacity: 0.7 }}>
                      66.7%
                    </span>
                  </div>
                  <div 
                    className="w-full h-2 rounded-full overflow-hidden"
                    style={{ backgroundColor: 'rgba(255, 255, 255, 0.1)' }}
                  >
                    <motion.div
                      className="h-full rounded-full"
                      style={{ backgroundColor: '#10b981' }}
                      initial={{ width: 0 }}
                      animate={{ width: '66.7%' }}
                      transition={{ duration: 1, delay: 1.2 + index * 0.1 }}
                    />
                  </div>
                </motion.div>
              )}

              {/* Sparkle Effect */}
              <motion.div
                className="absolute top-2 right-2 w-1 h-1 rounded-full"
                style={{ backgroundColor: themeConfig.accentColor }}
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 1, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: index * 0.5
                }}
              />
            </CardContent>
          </Card>
        </motion.div>
      ))}
    </div>
  );
};