import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from './card';
import { Avatar, AvatarFallback } from './avatar';
import { Badge } from './badge';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import {
  CheckCircledIcon,
  ClockIcon,
  ChatBubbleIcon,
  FileTextIcon,
  PersonIcon
} from '@radix-ui/react-icons';

interface ActivityItem {
  id: string;
  type: 'resolved' | 'submitted' | 'message' | 'update';
  title: string;
  description: string;
  time: string;
  user?: string;
  status?: string;
}

const activityData: ActivityItem[] = [
  {
    id: '1',
    type: 'resolved',
    title: 'Grievance Resolved',
    description: 'Road repair complaint #GR-001 has been successfully resolved',
    time: '2 hours ago',
    user: 'Admin',
    status: 'Completed'
  },
  {
    id: '2',
    type: 'message',
    title: 'New Message',
    description: 'You have received a response to your water supply complaint',
    time: '4 hours ago',
    user: 'Support Team'
  },
  {
    id: '3',
    type: 'submitted',
    title: 'Grievance Submitted',
    description: 'Street light maintenance request #GR-003 submitted',
    time: '1 day ago',
    user: 'You',
    status: 'In Progress'
  },
  {
    id: '4',
    type: 'update',
    title: 'Status Update',
    description: 'Garbage collection complaint moved to review stage',
    time: '2 days ago',
    user: 'System',
    status: 'Under Review'
  },
  {
    id: '5',
    type: 'message',
    title: 'Chat Response',
    description: 'AI assistant provided solution for your query',
    time: '3 days ago',
    user: 'AI Assistant'
  }
];

export const ActivityFeed: React.FC = () => {
  const { themeConfig } = useGradientTheme();

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'resolved':
        return CheckCircledIcon;
      case 'submitted':
        return FileTextIcon;
      case 'message':
        return ChatBubbleIcon;
      case 'update':
        return ClockIcon;
      default:
        return PersonIcon;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'resolved':
        return '#10b981';
      case 'submitted':
        return '#3b82f6';
      case 'message':
        return '#8b5cf6';
      case 'update':
        return '#f59e0b';
      default:
        return themeConfig.accentColor;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'Completed':
        return '#10b981';
      case 'In Progress':
        return '#f59e0b';
      case 'Under Review':
        return '#3b82f6';
      default:
        return '#6b7280';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 1.1 }}
    >
      <Card
        className="border-0 h-fit"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          background: 'var(--glass-bg)',
          border: '1px solid var(--glass-border)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}
      >
        <CardHeader>
          <CardTitle 
            className="text-xl font-bold"
            style={{ color: 'var(--theme-text)' }}
          >
            Activity Feed
          </CardTitle>
          <p 
            className="text-sm opacity-70"
            style={{ color: 'var(--theme-text)' }}
          >
            Recent updates and notifications
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {activityData.map((item, index) => {
              const IconComponent = getActivityIcon(item.type);
              const activityColor = getActivityColor(item.type);
              
              return (
                <motion.div
                  key={item.id}
                  className="flex gap-3 p-3 rounded-xl transition-all duration-300 cursor-pointer group"
                  style={{ backgroundColor: 'transparent' }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: 1.2 + index * 0.1 }}
                  whileHover={{ scale: 1.01 }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  {/* Activity Icon */}
                  <motion.div
                    className="flex-shrink-0 p-2 rounded-xl"
                    style={{
                      backgroundColor: `${activityColor}20`,
                      border: `1px solid ${activityColor}30`
                    }}
                    whileHover={{ rotate: 5 }}
                    transition={{ duration: 0.2 }}
                  >
                    <IconComponent 
                      className="w-4 h-4"
                      style={{ color: activityColor }}
                    />
                  </motion.div>

                  {/* Activity Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between gap-2 mb-1">
                      <h4 
                        className="font-semibold text-sm"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {item.title}
                      </h4>
                      {item.status && (
                        <Badge 
                          className="text-xs px-2 py-1 rounded-full font-medium flex-shrink-0"
                          style={{ 
                            backgroundColor: `${getStatusColor(item.status)}20`,
                            color: getStatusColor(item.status),
                            border: `1px solid ${getStatusColor(item.status)}40`
                          }}
                        >
                          {item.status}
                        </Badge>
                      )}
                    </div>
                    
                    <p 
                      className="text-xs opacity-70 mb-2 line-clamp-2"
                      style={{ color: 'var(--theme-text)' }}
                    >
                      {item.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Avatar className="h-5 w-5">
                          <AvatarFallback 
                            className="text-xs font-medium"
                            style={{ 
                              backgroundColor: `${activityColor}30`,
                              color: activityColor
                            }}
                          >
                            {item.user?.charAt(0) || 'S'}
                          </AvatarFallback>
                        </Avatar>
                        <span 
                          className="text-xs opacity-60"
                          style={{ color: 'var(--theme-text)' }}
                        >
                          {item.user}
                        </span>
                      </div>
                      
                      <span 
                        className="text-xs opacity-50"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {item.time}
                      </span>
                    </div>
                  </div>

                  {/* Hover Indicator */}
                  <motion.div
                    className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex-shrink-0"
                    initial={{ x: -10 }}
                    whileHover={{ x: 0 }}
                  >
                    <div 
                      className="w-1 h-8 rounded-full"
                      style={{ backgroundColor: activityColor }}
                    />
                  </motion.div>
                </motion.div>
              );
            })}
          </div>

          {/* Load More Button */}
          <motion.div
            className="mt-6 pt-4"
            style={{ borderTop: '1px solid var(--glass-border)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 2 }}
          >
            <motion.button
              className="w-full py-2 px-4 rounded-xl text-sm font-medium transition-all duration-300"
              style={{
                backgroundColor: 'transparent',
                color: 'var(--theme-text)',
                border: '1px solid var(--glass-border)'
              }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                e.currentTarget.style.borderColor = themeConfig.accentColor + '40';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'transparent';
                e.currentTarget.style.borderColor = 'var(--glass-border)';
              }}
            >
              Load More Activities
            </motion.button>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};