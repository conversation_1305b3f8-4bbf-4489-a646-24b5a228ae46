import React from 'react';
import { motion } from 'framer-motion';
import { SidebarNavigation } from './SidebarNavigation';
import { TopNavigation } from './TopNavigation';
import { StatsCards } from './StatsCards';
import { ActivityFeed } from './ActivityFeed';
import { QuickActions } from './QuickActions';

export const DashboardLayout: React.FC = () => {
  return (
    <div className="min-h-screen">
      <SidebarNavigation />
      <TopNavigation />
      
      {/* Main Content */}
      <motion.main
        className="ml-[270px] pt-20 p-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.3 }}
      >
        <div className="max-w-7xl mx-auto">
          {/* Welcome Section */}
          <motion.div
            className="mb-8"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <h1 
              className="text-3xl font-bold mb-2"
              style={{ color: 'var(--theme-text)' }}
            >
              Welcome back, User
            </h1>
            <p 
              className="opacity-70"
              style={{ color: 'var(--theme-text)' }}
            >
              Here's what's happening with your grievances today.
            </p>
          </motion.div>

          {/* Stats Cards */}
          <StatsCards />

          {/* Main Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-8">
            {/* Left Column - Quick Actions */}
            <div className="lg:col-span-2">
              <QuickActions />
            </div>

            {/* Right Column - Activity Feed */}
            <div>
              <ActivityFeed />
            </div>
          </div>
        </div>
      </motion.main>
    </div>
  );
};