import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { useTheme } from "../../providers/ThemeProvider";
import { useAuthStore } from "../../store/useAuthStore";
import { NavThemeSelector } from "./NavThemeSelector";
import {
  DashboardIcon,
  PersonIcon,
  MixerHorizontalIcon,
  FileTextIcon,
  BellIcon,
  ExitIcon,
  HamburgerMenuIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  MoonIcon,
} from "@radix-ui/react-icons";

interface HamburgerNavProps {
  className?: string;
}

const navigationItems = [
  { icon: DashboardIcon, label: "Dashboard", href: "/dashboard" },
  { icon: PersonIcon, label: "Profile", href: "/profile" },
  { icon: FileTextIcon, label: "Grievances", href: "/grievances" },
  { icon: BellIcon, label: "Notifications", href: "/notifications" },
  { icon: MixerHorizontalIcon, label: "Settings", href: "/settings" },
];

export const HamburgerNav: React.FC<HamburgerNavProps> = ({
  className = "",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isCompact, setIsCompact] = useState(false);
  const { themeConfig, currentTheme, setTheme } = useTheme();
  const { logout } = useAuthStore();

  const handleToggle = () => {
    if (!isOpen) {
      setIsOpen(true);
      setIsCompact(false);
    } else if (!isCompact) {
      setIsCompact(true);
    } else {
      setIsOpen(false);
      setIsCompact(false);
    }
  };

  const handleLogout = () => {
    logout();
    setIsOpen(false);
    setIsCompact(false);
  };

  return (
    <>
      {/* Hamburger Button */}
      <motion.button
        onClick={handleToggle}
        className={`fixed top-2 left-2 z-50 glass p-2 backdrop-blur-md transition-all duration-300 ${className}`}
        style={{
          borderRadius: "var(--radius-lg)",
          backgroundColor: themeConfig
            ? `rgb(${themeConfig.colors.card} / 0.8)`
            : undefined,
          boxShadow: themeConfig
            ? `0 0 15px rgb(${themeConfig.colors.primary} / 0.2)`
            : undefined,
          borderColor: themeConfig
            ? `rgb(${themeConfig.colors.border} / 0.3)`
            : undefined,
        }}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <HamburgerMenuIcon
          className="w-4 h-4"
          style={{
            color: themeConfig
              ? `rgb(${themeConfig.colors.primary})`
              : undefined,
          }}
        />
      </motion.button>

      {/* Navigation Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed top-2 left-2 h-[calc(100vh-1rem)] z-40 flex flex-col"
            initial={{ width: 0, opacity: 0 }}
            animate={{
              width: isCompact ? 60 : 240,
              opacity: 1,
            }}
            exit={{ width: 0, opacity: 0 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            style={{
              background: `linear-gradient(135deg, 
                rgba(255, 255, 255, 0.1) 0%, 
                rgba(255, 255, 255, 0.05) 25%, 
                ${themeConfig ? `rgba(${themeConfig.colors.primary.split(" ").join(", ")}, 0.1)` : 'rgba(139, 69, 255, 0.1)'} 50%, 
                ${themeConfig ? `rgba(${themeConfig.colors.accent.split(" ").join(", ")}, 0.08)` : 'rgba(59, 130, 246, 0.08)'} 75%, 
                rgba(255, 255, 255, 0.02) 100%)`,
              backdropFilter: "blur(30px)",
              WebkitBackdropFilter: "blur(30px)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              borderRadius: "var(--radius-3xl)",
              boxShadow: `0 20px 60px rgba(0, 0, 0, 0.1), 
                         0 0 40px ${themeConfig ? `rgba(${themeConfig.colors.primary.split(" ").join(", ")}, 0.1)` : 'rgba(139, 69, 255, 0.1)'}, 
                         inset 0 1px 0 rgba(255, 255, 255, 0.2)`,
            }}
          >
            {/* Header */}
            <div className="p-3 pb-2">
              <div className="flex items-center justify-end">
                <motion.button
                  onClick={() => setIsCompact(!isCompact)}
                  className="p-1.5 transition-all duration-200"
                  style={{
                    borderRadius: "var(--radius-full)",
                    background: themeConfig
                      ? `rgba(${themeConfig.colors.card
                          .split(" ")
                          .join(", ")}, 0.1)`
                      : "rgba(255, 255, 255, 0.05)",
                    backdropFilter: "blur(10px)",
                    WebkitBackdropFilter: "blur(10px)",
                  }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {isCompact ? (
                    <ChevronRightIcon
                      className="w-3 h-3"
                      style={{
                        color: themeConfig
                          ? `rgb(${themeConfig.colors.foreground})`
                          : undefined,
                      }}
                    />
                  ) : (
                    <ChevronLeftIcon
                      className="w-3 h-3"
                      style={{
                        color: themeConfig
                          ? `rgb(${themeConfig.colors.foreground})`
                          : undefined,
                      }}
                    />
                  )}
                </motion.button>
              </div>
            </div>

            {/* Navigation Items */}
            <div className="flex-1 px-3 py-4 space-y-6">
              {navigationItems.map((item, index) => (
                <motion.button
                  key={item.label}
                  className="w-full flex items-center gap-4 p-3 transition-all duration-300 group relative overflow-hidden"
                  style={{
                    background: `linear-gradient(135deg, 
                      rgba(255, 255, 255, 0.15) 0%, 
                      rgba(255, 255, 255, 0.08) 50%, 
                      rgba(255, 255, 255, 0.02) 100%)`,
                    backdropFilter: "blur(20px)",
                    WebkitBackdropFilter: "blur(20px)",
                    border: "1px solid rgba(255, 255, 255, 0.2)",
                    borderRadius: isCompact
                      ? "var(--radius-full)"
                      : "var(--radius-2xl)",
                    justifyContent: isCompact ? "center" : "flex-start",
                    boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
                  }}
                  whileHover={{
                    scale: 1.02,
                    y: -2,
                    boxShadow: "0 12px 40px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
                  }}
                  whileTap={{ scale: 0.99 }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.03 }}
                >
                  <motion.div
                    className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    style={{
                      background: `linear-gradient(135deg, 
                        rgba(255, 255, 255, 0.1) 0%, 
                        transparent 50%, 
                        rgba(255, 255, 255, 0.05) 100%)`,
                    }}
                  />
                  <item.icon
                    className="w-5 h-5 flex-shrink-0 relative z-10"
                    style={{ color: "#ffffff" }}
                  />
                  {!isCompact && (
                    <motion.span
                      className="font-medium text-sm relative z-10"
                      style={{ color: "#ffffff" }}
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                    >
                      {item.label}
                    </motion.span>
                  )}
                </motion.button>
              ))}
            </div>

            {/* Theme Selector */}
              <div className="px-3 py-3">
              <motion.button
                className="w-full flex items-center gap-4 p-3 transition-all duration-300 group relative overflow-hidden"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(255, 255, 255, 0.15) 0%, 
                    rgba(255, 255, 255, 0.08) 50%, 
                    rgba(255, 255, 255, 0.02) 100%)`,
                  backdropFilter: "blur(20px)",
                  WebkitBackdropFilter: "blur(20px)",
                  border: "1px solid rgba(255, 255, 255, 0.2)",
                  borderRadius: "var(--radius-2xl)",
                  justifyContent: "center",
                  boxShadow: "0 8px 32px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
                }}
                whileHover={{
                  scale: 1.02,
                  y: -2,
                  boxShadow: "0 12px 40px rgba(0, 0, 0, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
                }}
                whileTap={{ scale: 0.98 }}
              >
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: `linear-gradient(135deg, 
                      rgba(255, 255, 255, 0.1) 0%, 
                      transparent 50%, 
                      rgba(255, 255, 255, 0.05) 100%)`,
                  }}
                />
                <MoonIcon 
                  className="w-5 h-5 flex-shrink-0 relative z-10" 
                  style={{ color: "#ffffff" }}
                />
                <motion.span 
                  className="font-medium text-sm relative z-10"
                  style={{ color: "#ffffff" }}
                >
                  Theme
                </motion.span>
              </motion.button>
            </div>

            {/* Logout Button */}
            <div className="px-3 py-3 pb-6">
              <motion.button
                onClick={handleLogout}
                className="w-full flex items-center gap-4 p-3 transition-all duration-300 group relative overflow-hidden"
                style={{
                  background: `linear-gradient(135deg, 
                    rgba(255, 99, 99, 0.2) 0%, 
                    rgba(255, 99, 99, 0.1) 50%, 
                    rgba(255, 99, 99, 0.05) 100%)`,
                  backdropFilter: "blur(20px)",
                  WebkitBackdropFilter: "blur(20px)",
                  border: "1px solid rgba(255, 99, 99, 0.3)",
                  borderRadius: isCompact
                    ? "var(--radius-full)"
                    : "var(--radius-2xl)",
                  justifyContent: isCompact ? "center" : "flex-start",
                  boxShadow: "0 8px 32px rgba(255, 99, 99, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.2)",
                }}
                whileHover={{
                  scale: 1.02,
                  y: -2,
                  rotate: isCompact ? 10 : 0,
                  boxShadow: "0 12px 40px rgba(255, 99, 99, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3)",
                }}
                whileTap={{ scale: 0.99 }}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.div
                  className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  style={{
                    background: `linear-gradient(135deg, 
                      rgba(255, 99, 99, 0.1) 0%, 
                      transparent 50%, 
                      rgba(255, 99, 99, 0.05) 100%)`,
                  }}
                />
                <ExitIcon
                  className="w-5 h-5 flex-shrink-0 relative z-10"
                  style={{ color: "#ffffff" }}
                />
                {!isCompact && (
                  <motion.span
                    className="font-medium text-sm relative z-10"
                    style={{ color: "#ffffff" }}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    Log Out
                  </motion.span>
                )}
              </motion.button>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
