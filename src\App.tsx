import React, { useEffect } from "react";
import { ThemeProvider } from "./providers/ThemeProvider";
import { AuthPage } from "./features/auth/pages/AuthPage";
import { Dashboard } from "./pages/Dashboard";
import { useAuthStore } from "./store/useAuthStore";
import { Toaster } from "./components/ui/sonner";
import { apiConfig } from "./services/apiConfig";
import "./styles/globals.css";

function App() {
  const { initializeAuth, isAuthenticated } = useAuthStore();

  useEffect(() => {
    const initializeApp = async () => {
      try {
        await apiConfig.initialize();
        console.log("✅ API configuration initialized");
      } catch (error) {
        console.error("❌ Failed to initialize API configuration:", error);
      }
      initializeAuth();
    };

    initializeApp();
  }, [initializeAuth]);

  const handleAuthSuccess = () => {
    // Auth success is handled by the store
  };

  return (
    <ThemeProvider>
      {isAuthenticated ? (
        <Dashboard />
      ) : (
        <AuthPage onAuthSuccess={handleAuthSuccess} />
      )}
      <Toaster />
    </ThemeProvider>
  );
}

export default App;
