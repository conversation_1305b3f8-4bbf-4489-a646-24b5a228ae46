import React from 'react';
import { motion } from 'framer-motion';
import { But<PERSON> } from './button';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import {
  PlusIcon,
  ChatBubbleIcon,
  FileTextIcon,
  MagnifyingGlassIcon
} from '@radix-ui/react-icons';

export const QuickActions: React.FC = () => {
  const { themeConfig } = useGradientTheme();

  const actions = [
    {
      title: 'New Grievance',
      description: 'Submit a new grievance or complaint',
      icon: PlusIcon,
      color: '#10b981',
      action: () => console.log('New Grievance')
    },
    {
      title: 'Start Chat',
      description: 'Get instant help from our AI assistant',
      icon: ChatBubbleIcon,
      color: '#3b82f6',
      action: () => console.log('Start Chat')
    },
    {
      title: 'View All',
      description: 'Browse all your submitted grievances',
      icon: FileTextIcon,
      color: '#f59e0b',
      action: () => console.log('View All')
    },
    {
      title: 'Search',
      description: 'Find specific grievances or information',
      icon: MagnifyingGlassIcon,
      color: '#8b5cf6',
      action: () => console.log('Search')
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.9 }}
    >
      <Card
        className="border-0"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          background: 'var(--glass-bg)',
          border: '1px solid var(--glass-border)',
          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
        }}
      >
        <CardHeader>
          <CardTitle 
            className="text-xl font-bold"
            style={{ color: 'var(--theme-text)' }}
          >
            Quick Actions
          </CardTitle>
          <p 
            className="text-sm opacity-70"
            style={{ color: 'var(--theme-text)' }}
          >
            Common tasks and shortcuts
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {actions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4, delay: 1 + index * 0.1 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="ghost"
                  className="w-full h-auto p-4 rounded-xl transition-all duration-300 group"
                  style={{
                    backgroundColor: 'transparent',
                    border: '1px solid var(--glass-border)'
                  }}
                  onClick={action.action}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                    e.currentTarget.style.borderColor = `${action.color}40`;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.borderColor = 'var(--glass-border)';
                  }}
                >
                  <div className="flex items-start gap-4 w-full">
                    <motion.div
                      className="p-3 rounded-xl flex-shrink-0"
                      style={{
                        backgroundColor: `${action.color}20`,
                        border: `1px solid ${action.color}30`
                      }}
                      whileHover={{ rotate: 5 }}
                      transition={{ duration: 0.2 }}
                    >
                      <action.icon 
                        className="w-5 h-5"
                        style={{ color: action.color }}
                      />
                    </motion.div>
                    <div className="flex-1 text-left">
                      <h3 
                        className="font-semibold mb-1"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {action.title}
                      </h3>
                      <p 
                        className="text-sm opacity-70"
                        style={{ color: 'var(--theme-text)' }}
                      >
                        {action.description}
                      </p>
                    </div>
                    <motion.div
                      className="opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                      initial={{ x: -10 }}
                      whileHover={{ x: 0 }}
                    >
                      <div 
                        className="w-2 h-2 rounded-full"
                        style={{ backgroundColor: action.color }}
                      />
                    </motion.div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Recent Activity Preview */}
          <motion.div
            className="mt-6 pt-6"
            style={{ borderTop: '1px solid var(--glass-border)' }}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.5 }}
          >
            <h4 
              className="font-semibold mb-3"
              style={{ color: 'var(--theme-text)' }}
            >
              Recent Activity
            </h4>
            <div className="space-y-2">
              {[
                { action: 'Grievance #GR-001 resolved', time: '2 hours ago', status: 'success' },
                { action: 'New message from admin', time: '1 day ago', status: 'info' },
                { action: 'Grievance #GR-002 submitted', time: '3 days ago', status: 'pending' }
              ].map((item, index) => (
                <motion.div
                  key={index}
                  className="flex items-center gap-3 p-2 rounded-lg transition-all duration-200"
                  style={{ backgroundColor: 'transparent' }}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.6 + index * 0.1 }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }}
                >
                  <div 
                    className="w-2 h-2 rounded-full flex-shrink-0"
                    style={{ 
                      backgroundColor: item.status === 'success' ? '#10b981' : 
                                     item.status === 'info' ? '#3b82f6' : '#f59e0b'
                    }}
                  />
                  <div className="flex-1">
                    <p 
                      className="text-sm"
                      style={{ color: 'var(--theme-text)' }}
                    >
                      {item.action}
                    </p>
                    <p 
                      className="text-xs opacity-60"
                      style={{ color: 'var(--theme-text)' }}
                    >
                      {item.time}
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  );
};