import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from './button';
import { Avatar, AvatarFallback } from './avatar';
import { useGradientTheme } from '../../providers/GradientThemeProvider';
import { gradientThemes, GradientThemeName } from '../../types/gradientThemes';
import {
  HomeIcon,
  FileTextIcon,
  ActivityLogIcon,
  GearIcon,
  ColorWheelIcon,
  ExitIcon,
  ChevronDownIcon
} from '@radix-ui/react-icons';

interface NavigationItem {
  icon: React.ComponentType<{ className?: string }>;
  label: string;
  href: string;
  active?: boolean;
}

const navigationItems: NavigationItem[] = [
  { icon: HomeIcon, label: 'Overview', href: '/dashboard', active: true },
  { icon: FileTextIcon, label: 'Grievances', href: '/grievances' },
  { icon: ActivityLogIcon, label: 'Activity', href: '/activity' },
  { icon: GearIcon, label: 'Settings', href: '/settings' }
];

export const SidebarNavigation: React.FC = () => {
  const { currentTheme, setTheme, themeConfig } = useGradientTheme();
  const [isThemeOpen, setIsThemeOpen] = useState(false);

  const handleLogout = () => {
    // Logout logic here
    console.log('Logout clicked');
  };

  return (
    <motion.div
      className="fixed left-0 top-0 h-screen w-[270px] z-50 p-6"
      initial={{ x: -270, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: 'easeOut' }}
    >
      <div
        className="h-full rounded-2xl p-4 flex flex-col"
        style={{
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          background: 'var(--glass-bg)',
          border: '1px solid var(--glass-border)',
          boxShadow: '0 10px 30px rgba(0, 0, 0, 0.1)'
        }}
      >
        {/* User Info Panel */}
        <motion.div
          className="flex items-center gap-3 mb-8"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Avatar className="h-12 w-12">
            <AvatarFallback 
              className="font-semibold text-white"
              style={{ backgroundColor: themeConfig.accentColor }}
            >
              U
            </AvatarFallback>
          </Avatar>
          <div className="flex flex-col">
            <span 
              className="text-sm font-semibold"
              style={{ color: 'var(--theme-text)' }}
            >
              User
            </span>
            <span 
              className="text-xs opacity-60"
              style={{ color: 'var(--theme-text)' }}
            >
              Citizen
            </span>
          </div>
        </motion.div>

        {/* Navigation Items */}
        <nav className="flex-1 space-y-2">
          {navigationItems.map((item, index) => (
            <motion.div
              key={item.label}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Button
                variant="ghost"
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-xl transition-all duration-200 justify-start ${
                  item.active 
                    ? 'font-medium' 
                    : 'font-normal'
                }`}
                style={{
                  backgroundColor: item.active ? 'var(--glass-hover)' : 'transparent',
                  color: 'var(--theme-text)',
                  opacity: item.active ? 1 : 0.7
                }}
                onMouseEnter={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                    e.currentTarget.style.opacity = '1';
                  }
                }}
                onMouseLeave={(e) => {
                  if (!item.active) {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.opacity = '0.7';
                  }
                }}
              >
                <item.icon className="w-[18px] h-[18px]" />
                <span className="text-sm">{item.label}</span>
              </Button>
            </motion.div>
          ))}
        </nav>

        {/* Theme Selector */}
        <motion.div
          className="mb-5 relative"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
        >
          <Button
            variant="ghost"
            className="w-full flex items-center gap-3 px-3 py-2 rounded-xl transition-all duration-200 justify-start"
            style={{
              color: 'var(--theme-text)',
              opacity: 0.7
            }}
            onClick={() => setIsThemeOpen(!isThemeOpen)}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.opacity = '0.7';
            }}
          >
            <ColorWheelIcon className="w-[18px] h-[18px]" />
            <span className="text-sm flex-1 text-left">{themeConfig.displayName}</span>
            <ChevronDownIcon 
              className={`w-4 h-4 transition-transform duration-200 ${isThemeOpen ? 'rotate-180' : ''}`} 
            />
          </Button>

          {/* Theme Dropdown */}
          <AnimatePresence>
            {isThemeOpen && (
              <motion.div
                className="absolute bottom-full left-0 mb-2 w-full max-h-48 overflow-y-auto rounded-xl p-2"
                style={{
                  backdropFilter: 'blur(20px)',
                  WebkitBackdropFilter: 'blur(20px)',
                  background: 'var(--glass-bg)',
                  border: '1px solid var(--glass-border)',
                  boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)'
                }}
                initial={{ opacity: 0, scale: 0.95, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.95, y: 10 }}
                transition={{ duration: 0.2 }}
              >
                {Object.entries(gradientThemes).map(([themeName, theme]) => (
                  <Button
                    key={themeName}
                    variant="ghost"
                    className="w-full flex items-center gap-2 px-2 py-1.5 rounded-lg text-xs justify-start"
                    style={{
                      color: 'var(--theme-text)',
                      opacity: currentTheme === themeName ? 1 : 0.6
                    }}
                    onClick={() => {
                      setTheme(themeName as GradientThemeName);
                      setIsThemeOpen(false);
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--glass-hover)';
                      e.currentTarget.style.opacity = '1';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.opacity = currentTheme === themeName ? '1' : '0.6';
                    }}
                  >
                    <div 
                      className="w-3 h-3 rounded-full border border-white/20"
                      style={{ background: theme.gradient }}
                    />
                    {theme.displayName}
                  </Button>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>

        {/* Logout Button */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
        >
          <Button
            variant="ghost"
            className="w-full flex items-center gap-3 px-3 py-2 rounded-xl transition-all duration-200 justify-start"
            style={{
              color: '#ef4444',
              opacity: 0.8
            }}
            onClick={handleLogout}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'rgba(239, 68, 68, 0.1)';
              e.currentTarget.style.opacity = '1';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.opacity = '0.8';
            }}
          >
            <ExitIcon className="w-[18px] h-[18px]" />
            <span className="text-sm font-medium">Logout</span>
          </Button>
        </motion.div>
      </div>
    </motion.div>
  );
};